"""
Actor Summary Agent - LLM-powered Threat Actor Analysis and Summarization
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

import aiohttp
from pydantic import BaseModel

# Import Cyfirma integration
from ..services.cyfirma_service import CyfirmaService
from ..schemas.threat_actor import ProcessedThreatActor, ThreatActorSearchRequest, ThreatActorSearchResponse


@dataclass
class ThreatActor:
    """Threat Actor data model"""
    name: str
    aliases: List[str]
    description: str
    first_seen: datetime
    last_activity: Optional[datetime] = None
    origin_country: Optional[str] = None
    motivation: List[str] = None
    target_industries: List[str] = None
    target_regions: List[str] = None
    ttps: List[str] = None  # MITRE ATT&CK techniques
    associated_malware: List[str] = None
    iocs: List[str] = None
    confidence_score: float = 0.0
    sources: List[str] = None

    def __post_init__(self):
        if self.motivation is None:
            self.motivation = []
        if self.target_industries is None:
            self.target_industries = []
        if self.target_regions is None:
            self.target_regions = []
        if self.ttps is None:
            self.ttps = []
        if self.associated_malware is None:
            self.associated_malware = []
        if self.iocs is None:
            self.iocs = []
        if self.sources is None:
            self.sources = []


class ActorSummaryResponse(BaseModel):
    """Response model for actor summary"""
    executive_summary: str
    attack_vectors: List[str]
    target_analysis: Dict[str, List[str]]
    mitre_techniques: List[Dict[str, str]]
    risk_assessment: Dict[str, Any]
    recommendations: List[str]
    confidence_level: str


class ActorSummaryAgent:
    """LLM-powered threat actor analysis and summarization agent"""

    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # LLM service configurations
        self.llm_services = {
            'openai': self._query_openai,
            'deepseek': self._query_deepseek,
            'ollama': self._query_ollama
        }

        # MITRE ATT&CK technique mapping
        self.mitre_techniques = self._load_mitre_techniques()

    def _load_mitre_techniques(self) -> Dict[str, str]:
        """Load MITRE ATT&CK technique mappings"""
        # This would typically load from a database or file
        # For now, returning a sample mapping
        return {
            "T1566": "Phishing",
            "T1059": "Command and Scripting Interpreter",
            "T1055": "Process Injection",
            "T1083": "File and Directory Discovery",
            "T1082": "System Information Discovery",
            "T1005": "Data from Local System",
            "T1041": "Exfiltration Over C2 Channel",
            "T1071": "Application Layer Protocol",
            "T1105": "Ingress Tool Transfer",
            "T1027": "Obfuscated Files or Information"
        }

    async def analyze_threat_actor(self, actor: ThreatActor) -> ActorSummaryResponse:
        """Analyze threat actor and generate comprehensive summary"""

        # Prepare context for LLM
        actor_context = self._prepare_actor_context(actor)

        # Generate summary using configured LLM service
        llm_service = self.config.get('llm_service', 'openai')

        if llm_service in self.llm_services:
            summary_data = await self.llm_services[llm_service](actor_context)
        else:
            raise ValueError(f"Unsupported LLM service: {llm_service}")

        # Parse and validate response
        summary = self._parse_llm_response(summary_data)

        # Enhance with MITRE ATT&CK mapping
        summary = self._enhance_with_mitre_mapping(summary, actor.ttps)

        return summary

    def _prepare_actor_context(self, actor: ThreatActor) -> str:
        """Prepare threat actor context for LLM analysis"""

        context = f"""
        THREAT ACTOR ANALYSIS REQUEST

        Actor Name: {actor.name}
        Aliases: {', '.join(actor.aliases) if actor.aliases else 'None'}
        Description: {actor.description}

        First Seen: {actor.first_seen.strftime('%Y-%m-%d') if actor.first_seen else 'Unknown'}
        Last Activity: {actor.last_activity.strftime('%Y-%m-%d') if actor.last_activity else 'Unknown'}
        Origin Country: {actor.origin_country or 'Unknown'}

        Motivation: {', '.join(actor.motivation) if actor.motivation else 'Unknown'}
        Target Industries: {', '.join(actor.target_industries) if actor.target_industries else 'Unknown'}
        Target Regions: {', '.join(actor.target_regions) if actor.target_regions else 'Unknown'}

        Known TTPs: {', '.join(actor.ttps) if actor.ttps else 'None'}
        Associated Malware: {', '.join(actor.associated_malware) if actor.associated_malware else 'None'}

        Associated IoCs: {len(actor.iocs)} indicators
        Confidence Score: {actor.confidence_score}
        Sources: {', '.join(actor.sources) if actor.sources else 'None'}
        """

        return context.strip()

    async def _query_openai(self, context: str) -> Dict:
        """Query OpenAI GPT for threat actor analysis"""

        prompt = f"""
        Analyze the following threat actor data and provide a comprehensive summary:

        {context}

        Please provide:
        1. Executive summary of the threat actor (2-3 sentences)
        2. Primary attack vectors and techniques
        3. Target analysis (industries and regions)
        4. MITRE ATT&CK technique mapping
        5. Risk assessment and threat level
        6. Defensive recommendations

        Format the response as structured JSON with the following keys:
        - executive_summary
        - attack_vectors (array)
        - target_analysis (object with industries and regions arrays)
        - mitre_techniques (array of objects with id and name)
        - risk_assessment (object with threat_level, impact_score, likelihood)
        - recommendations (array)
        - confidence_level (High/Medium/Low)
        """

        if not self.config.get('openai_api_key'):
            raise ValueError("OpenAI API key not configured")

        headers = {
            'Authorization': f"Bearer {self.config['openai_api_key']}",
            'Content-Type': 'application/json'
        }

        payload = {
            'model': self.config.get('openai_model', 'gpt-4'),
            'messages': [
                {
                    'role': 'system',
                    'content': 'You are a cybersecurity threat intelligence analyst specializing in threat actor profiling and MITRE ATT&CK framework mapping.'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.3,
            'max_tokens': 2000
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'https://api.openai.com/v1/chat/completions',
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        content = data['choices'][0]['message']['content']

                        # Try to parse as JSON
                        try:
                            return json.loads(content)
                        except json.JSONDecodeError:
                            # Fallback to text parsing
                            return {'raw_response': content}
                    else:
                        error_data = await response.json()
                        raise Exception(f"OpenAI API error: {error_data}")

        except Exception as e:
            self.logger.error(f"OpenAI query error: {e}")
            raise

    async def _query_deepseek(self, context: str) -> Dict:
        """Query DeepSeek API for threat actor analysis"""
        # Similar implementation to OpenAI but with DeepSeek endpoints
        # This is a placeholder - implement based on DeepSeek API documentation
        return {'raw_response': 'DeepSeek integration not implemented yet'}

    async def _query_ollama(self, context: str) -> Dict:
        """Query local Ollama instance for threat actor analysis"""

        prompt = f"""
        Analyze the following threat actor data and provide a comprehensive summary:

        {context}

        Please provide:
        1. Executive summary of the threat actor
        2. Primary attack vectors and techniques
        3. Target analysis (industries and regions)
        4. MITRE ATT&CK technique mapping
        5. Risk assessment and threat level
        6. Defensive recommendations

        Format as JSON with keys: executive_summary, attack_vectors, target_analysis, mitre_techniques, risk_assessment, recommendations, confidence_level
        """

        payload = {
            'model': self.config.get('ollama_model', 'llama2'),
            'prompt': prompt,
            'stream': False
        }

        try:
            ollama_url = self.config.get('ollama_url', 'http://localhost:11434')

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f'{ollama_url}/api/generate',
                    json=payload
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        content = data.get('response', '')

                        # Try to parse as JSON
                        try:
                            return json.loads(content)
                        except json.JSONDecodeError:
                            return {'raw_response': content}
                    else:
                        raise Exception(f"Ollama API error: {response.status}")

        except Exception as e:
            self.logger.error(f"Ollama query error: {e}")
            raise

    def _parse_llm_response(self, response_data: Dict) -> ActorSummaryResponse:
        """Parse LLM response into structured format"""

        if 'raw_response' in response_data:
            # Handle raw text response - basic parsing
            return ActorSummaryResponse(
                executive_summary=response_data.get('raw_response', 'No summary available'),
                attack_vectors=[],
                target_analysis={'industries': [], 'regions': []},
                mitre_techniques=[],
                risk_assessment={'threat_level': 'Unknown', 'impact_score': 0, 'likelihood': 0},
                recommendations=[],
                confidence_level='Low'
            )

        # Handle structured JSON response
        return ActorSummaryResponse(
            executive_summary=response_data.get('executive_summary', 'No summary available'),
            attack_vectors=response_data.get('attack_vectors', []),
            target_analysis=response_data.get('target_analysis', {'industries': [], 'regions': []}),
            mitre_techniques=response_data.get('mitre_techniques', []),
            risk_assessment=response_data.get('risk_assessment', {'threat_level': 'Unknown'}),
            recommendations=response_data.get('recommendations', []),
            confidence_level=response_data.get('confidence_level', 'Medium')
        )

    def _enhance_with_mitre_mapping(self, summary: ActorSummaryResponse, ttps: List[str]) -> ActorSummaryResponse:
        """Enhance summary with MITRE ATT&CK technique mapping"""

        enhanced_techniques = []

        for ttp in ttps:
            if ttp in self.mitre_techniques:
                enhanced_techniques.append({
                    'id': ttp,
                    'name': self.mitre_techniques[ttp],
                    'source': 'mitre_attack'
                })

        # Merge with existing techniques from LLM
        existing_techniques = summary.mitre_techniques or []
        all_techniques = existing_techniques + enhanced_techniques

        # Remove duplicates based on technique ID
        unique_techniques = []
        seen_ids = set()

        for technique in all_techniques:
            technique_id = technique.get('id', '')
            if technique_id not in seen_ids:
                unique_techniques.append(technique)
                seen_ids.add(technique_id)

        summary.mitre_techniques = unique_techniques

        return summary

    async def batch_analyze_actors(self, actors: List[ThreatActor]) -> List[ActorSummaryResponse]:
        """Batch analyze multiple threat actors"""

        tasks = []
        for actor in actors:
            task = self.analyze_threat_actor(actor)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and return successful results
        successful_summaries = [result for result in results if isinstance(result, ActorSummaryResponse)]

        return successful_summaries

    def generate_actor_report(self, actor: ThreatActor, summary: ActorSummaryResponse) -> str:
        """Generate a formatted threat actor report"""

        report = f"""
# THREAT ACTOR INTELLIGENCE REPORT

## Actor Information
**Name:** {actor.name}
**Aliases:** {', '.join(actor.aliases) if actor.aliases else 'None'}
**First Seen:** {actor.first_seen.strftime('%Y-%m-%d') if actor.first_seen else 'Unknown'}
**Last Activity:** {actor.last_activity.strftime('%Y-%m-%d') if actor.last_activity else 'Unknown'}
**Origin:** {actor.origin_country or 'Unknown'}

## Executive Summary
{summary.executive_summary}

## Attack Vectors
{chr(10).join(f"• {vector}" for vector in summary.attack_vectors)}

## Target Analysis
**Industries:** {', '.join(summary.target_analysis.get('industries', []))}
**Regions:** {', '.join(summary.target_analysis.get('regions', []))}

## MITRE ATT&CK Techniques
{chr(10).join(f"• {tech.get('id', '')}: {tech.get('name', '')}" for tech in summary.mitre_techniques)}

## Risk Assessment
**Threat Level:** {summary.risk_assessment.get('threat_level', 'Unknown')}
**Impact Score:** {summary.risk_assessment.get('impact_score', 'N/A')}
**Likelihood:** {summary.risk_assessment.get('likelihood', 'N/A')}

## Recommendations
{chr(10).join(f"• {rec}" for rec in summary.recommendations)}

**Confidence Level:** {summary.confidence_level}
**Report Generated:** {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}
        """

        return report.strip()

    async def search_cyfirma_actors(self, search_request: ThreatActorSearchRequest) -> ThreatActorSearchResponse:
        """Search Cyfirma threat actors with filtering"""
        start_time = datetime.utcnow()

        try:
            # Get Cyfirma API key from config
            cyfirma_api_key = self.config.get('cyfirma_api_key')
            if not cyfirma_api_key:
                raise ValueError("Cyfirma API key not configured")

            async with CyfirmaService(cyfirma_api_key) as cyfirma:
                # Fetch all actors (with caching)
                api_response = await cyfirma.fetch_all_threat_actors()

                if not api_response.success:
                    raise Exception(f"Cyfirma API error: {api_response.error_message}")

                # Process and filter actors
                processed_actors = []
                for cyfirma_actor in api_response.data:
                    processed_actor = cyfirma.process_threat_actor(cyfirma_actor)

                    # Apply filters
                    if self._matches_search_criteria(processed_actor, search_request):
                        processed_actors.append(processed_actor)

                # Sort by relevance/confidence
                processed_actors.sort(key=lambda x: x.confidence_score, reverse=True)

                # Apply pagination
                total_count = len(processed_actors)
                start_idx = search_request.offset
                end_idx = start_idx + search_request.limit
                paginated_actors = processed_actors[start_idx:end_idx]

                # Generate facets for filtering UI
                facets = self._generate_search_facets(processed_actors)

                query_time = (datetime.utcnow() - start_time).total_seconds() * 1000

                return ThreatActorSearchResponse(
                    total_count=total_count,
                    actors=paginated_actors,
                    facets=facets,
                    query_time_ms=query_time
                )

        except Exception as e:
            logger.error(f"Failed to search Cyfirma actors: {e}")
            return ThreatActorSearchResponse(
                total_count=0,
                actors=[],
                facets={},
                query_time_ms=(datetime.utcnow() - start_time).total_seconds() * 1000
            )

    def _matches_search_criteria(self, actor: ProcessedThreatActor, search: ThreatActorSearchRequest) -> bool:
        """Check if actor matches search criteria"""

        # Text search in name, description, aliases
        if search.query:
            query_lower = search.query.lower()
            searchable_text = f"{actor.name} {actor.description} {' '.join(actor.aliases)}".lower()
            if query_lower not in searchable_text:
                return False

        # Actor types filter
        if search.actor_types:
            if not any(actor_type in actor.actor_types for actor_type in search.actor_types):
                return False

        # Motivation filter
        if search.motivations and actor.primary_motivation:
            if actor.primary_motivation not in search.motivations:
                return False

        # Origin countries filter
        if search.origin_countries and actor.origin_country:
            if actor.origin_country not in search.origin_countries:
                return False

        # Target countries filter
        if search.target_countries:
            if not any(country in actor.target_countries for country in search.target_countries):
                return False

        # Target industries filter
        if search.target_industries:
            if not any(industry in actor.target_industries for industry in search.target_industries):
                return False

        # Date range filter
        if search.date_from and actor.modified < search.date_from:
            return False
        if search.date_to and actor.modified > search.date_to:
            return False

        return True

    def _generate_search_facets(self, actors: List[ProcessedThreatActor]) -> Dict[str, Dict[str, int]]:
        """Generate facets for search filtering UI"""
        facets = {
            "actor_types": {},
            "motivations": {},
            "origin_countries": {},
            "target_industries": {},
            "severity_levels": {}
        }

        for actor in actors:
            # Actor types
            for actor_type in actor.actor_types:
                facets["actor_types"][actor_type] = facets["actor_types"].get(actor_type, 0) + 1

            # Motivations
            if actor.primary_motivation:
                facets["motivations"][actor.primary_motivation] = facets["motivations"].get(actor.primary_motivation, 0) + 1

            # Origin countries
            if actor.origin_country:
                facets["origin_countries"][actor.origin_country] = facets["origin_countries"].get(actor.origin_country, 0) + 1

            # Target industries (top 10)
            for industry in actor.target_industries[:10]:
                facets["target_industries"][industry] = facets["target_industries"].get(industry, 0) + 1

            # Severity levels
            facets["severity_levels"][actor.severity_level] = facets["severity_levels"].get(actor.severity_level, 0) + 1

        return facets


# Example configuration
DEFAULT_CONFIG = {
    'llm_service': 'openai',  # 'openai', 'deepseek', 'ollama'
    'openai_api_key': '',
    'openai_model': 'gpt-4',
    'deepseek_api_key': '',
    'deepseek_model': 'deepseek-chat',
    'ollama_url': 'http://localhost:11434',
    'ollama_model': 'llama2'
}


async def main():
    """Example usage of Actor Summary Agent"""
    config = DEFAULT_CONFIG.copy()
    agent = ActorSummaryAgent(config)

    # Example threat actor
    test_actor = ThreatActor(
        name="APT29",
        aliases=["Cozy Bear", "The Dukes"],
        description="Advanced persistent threat group associated with Russian intelligence services",
        first_seen=datetime(2008, 1, 1),
        last_activity=datetime(2023, 12, 1),
        origin_country="Russia",
        motivation=["Espionage", "Intelligence gathering"],
        target_industries=["Government", "Defense", "Healthcare"],
        target_regions=["United States", "Europe", "NATO countries"],
        ttps=["T1566", "T1059", "T1055"],
        associated_malware=["CozyDuke", "MiniDuke", "OnionDuke"],
        confidence_score=0.9,
        sources=["MITRE", "CrowdStrike", "FireEye"]
    )

    try:
        summary = await agent.analyze_threat_actor(test_actor)
        report = agent.generate_actor_report(test_actor, summary)
        print(report)
    except Exception as e:
        print(f"Analysis failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())